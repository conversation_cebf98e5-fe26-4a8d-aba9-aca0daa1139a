实现一个自治域地理分布查询程序。
* 用户输入自治域号
* 使用BGPView查询自治域信息，获取宣告的IPv4前缀数据（示例：https://bgpview.io/asn/138915#prefixes-v4）
* 将前缀拆解为C段
* 使用Maxmind Geoip2数据库查询每个C段的国家、省份、城市（数据库文件为GeoLite2-City.mmdb，读取方法可以参考https://github.com/maxmind/GeoIP2-python）
* 将结果保存一个csv表格，每一行是一个C段，包括其国家、省份、城市信息
* 输出该自治域IP的国家分布和占比


Create a Python program that analyzes the geographical distribution of IP addresses for a given Autonomous System (AS). The program should:

**Input Requirements:**
- Accept an AS number (ASN) as user input (e.g., 138915)

**Data Collection:**
- Query the BGPView API to retrieve IPv4 prefix announcements for the specified ASN
  - Website: https://api.bgpview.io/asn/{asn}
  - Example for ASN 138915: https://bgpview.io/asn/138915#prefixes-v4
- Extract all announced IPv4 prefixes from the API response

**IP Address Processing:**
- Break down each IPv4 prefix into /24 subnets (Class C networks)
- For each /24 subnet, use the network address (first IP) for geolocation lookup

**Geolocation Analysis:**
- Use the MaxMind GeoLite2 database (GeoLite2-City.mmdb file) to determine geographical information
- For each /24 subnet, retrieve: country, state/province, and city
- Use the GeoIP2-python library (reference: https://github.com/maxmind/GeoIP2-python)
- Handle cases where geolocation data may be unavailable

**Output Requirements:**
1. **CSV File**: Create a detailed CSV file with columns:
   - Subnet (CIDR notation, e.g., "***********/24")
   - Country
   - State/Province  
   - City
   - One row per /24 subnet

2. **Summary Report**: Display statistics showing:
   - Country-wise distribution of IP subnets
   - Percentage breakdown by country
   - Total number of /24 subnets analyzed

**Technical Specifications:**
- Implement proper error handling for API failures and missing geolocation data
- Use appropriate Python libraries (requests for API calls, geoip2 for database queries, csv for file output)
- Ensure the program can handle large ASNs with many prefixes efficiently
- Include progress indicators for long-running operations