#!/usr/bin/env python3
"""
Autonomous System Geographical Distribution Analyzer

This program analyzes the geographical distribution of IP addresses 
for a given Autonomous System (AS) using BGPView API and MaxMind GeoIP2 database.
"""

import requests
import geoip2.database
import ipaddress
import csv
import sys
import time
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Optional
import argparse


class ASNGeoAnalyzer:
    """Main class for analyzing ASN geographical distribution."""
    
    def __init__(self, geoip_db_path: str = "GeoLite2-City.mmdb"):
        """
        Initialize the analyzer with GeoIP database.
        
        Args:
            geoip_db_path: Path to the MaxMind GeoLite2-City.mmdb file
        """
        self.geoip_db_path = geoip_db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ASN-Geo-Analyzer/1.0'
        })
    
    def get_asn_prefixes(self, asn: int) -> List[str]:
        """
        Retrieve IPv4 prefixes announced by the given ASN from BGPView API.
        
        Args:
            asn: Autonomous System Number
            
        Returns:
            List of IPv4 prefixes in CIDR notation
            
        Raises:
            requests.RequestException: If API request fails
        """
        url = f"https://api.bgpview.io/asn/{asn}"
        
        print(f"Querying BGPView API for ASN {asn}...")
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'ok':
                raise ValueError(f"BGPView API returned error: {data.get('status_message', 'Unknown error')}")
            
            asn_data = data.get('data', {})
            prefixes_v4 = asn_data.get('prefixes_v4', [])
            
            prefixes = [prefix['prefix'] for prefix in prefixes_v4]
            
            print(f"Found {len(prefixes)} IPv4 prefixes for ASN {asn}")
            return prefixes
            
        except requests.RequestException as e:
            raise requests.RequestException(f"Failed to query BGPView API: {e}")
    
    def break_into_c_class_subnets(self, prefixes: List[str]) -> List[str]:
        """
        Break down IPv4 prefixes into /24 (Class C) subnets.
        
        Args:
            prefixes: List of IPv4 prefixes in CIDR notation
            
        Returns:
            List of /24 subnets in CIDR notation
        """
        c_class_subnets = set()
        
        print("Breaking down prefixes into /24 subnets...")
        
        for prefix in prefixes:
            try:
                network = ipaddress.IPv4Network(prefix, strict=False)
                
                # If the prefix is larger than /24, break it down
                if network.prefixlen < 24:
                    for subnet in network.subnets(new_prefix=24):
                        c_class_subnets.add(str(subnet))
                # If it's /24 or smaller, use as is
                elif network.prefixlen >= 24:
                    # For subnets smaller than /24, use the /24 containing it
                    parent_24 = ipaddress.IPv4Network(f"{network.network_address}/{24}", strict=False)
                    c_class_subnets.add(str(parent_24))
                    
            except ipaddress.AddressValueError as e:
                print(f"Warning: Invalid prefix {prefix}: {e}")
                continue
        
        subnets_list = sorted(list(c_class_subnets))
        print(f"Generated {len(subnets_list)} unique /24 subnets")
        
        return subnets_list
    
    def geolocate_subnets(self, subnets: List[str]) -> List[Dict[str, str]]:
        """
        Geolocate each /24 subnet using MaxMind GeoIP2 database.
        
        Args:
            subnets: List of /24 subnets in CIDR notation
            
        Returns:
            List of dictionaries containing geolocation data
        """
        results = []
        
        print(f"Geolocating {len(subnets)} subnets using MaxMind database...")
        
        try:
            with geoip2.database.Reader(self.geoip_db_path) as reader:
                for i, subnet in enumerate(subnets):
                    if i % 100 == 0:
                        print(f"Progress: {i}/{len(subnets)} ({i/len(subnets)*100:.1f}%)")
                    
                    try:
                        # Get the network address (first IP) of the subnet
                        network = ipaddress.IPv4Network(subnet)
                        ip_to_lookup = str(network.network_address)
                        
                        # Query the GeoIP database
                        response = reader.city(ip_to_lookup)
                        
                        result = {
                            'subnet': subnet,
                            'country': response.country.name or 'Unknown',
                            'country_code': response.country.iso_code or 'Unknown',
                            'state': response.subdivisions.most_specific.name or 'Unknown',
                            'city': response.city.name or 'Unknown'
                        }
                        
                    except (geoip2.errors.AddressNotFoundError, ValueError):
                        # Handle cases where IP is not in database
                        result = {
                            'subnet': subnet,
                            'country': 'Unknown',
                            'country_code': 'Unknown',
                            'state': 'Unknown',
                            'city': 'Unknown'
                        }
                    
                    results.append(result)
                
        except FileNotFoundError:
            raise FileNotFoundError(f"GeoIP database file not found: {self.geoip_db_path}")
        except Exception as e:
            raise Exception(f"Error reading GeoIP database: {e}")
        
        print(f"Geolocation complete: {len(results)} subnets processed")
        return results
    
    def save_to_csv(self, results: List[Dict[str, str]], filename: str) -> None:
        """
        Save geolocation results to CSV file.
        
        Args:
            results: List of geolocation results
            filename: Output CSV filename
        """
        print(f"Saving results to {filename}...")
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['subnet', 'country', 'state', 'city']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow({
                    'subnet': result['subnet'],
                    'country': result['country'],
                    'state': result['state'],
                    'city': result['city']
                })
        
        print(f"CSV file saved successfully: {filename}")
    
    def generate_summary_report(self, results: List[Dict[str, str]]) -> None:
        """
        Generate and display summary statistics.
        
        Args:
            results: List of geolocation results
        """
        print("\n" + "="*60)
        print("GEOGRAPHICAL DISTRIBUTION SUMMARY REPORT")
        print("="*60)
        
        total_subnets = len(results)
        print(f"Total /24 subnets analyzed: {total_subnets}")
        
        # Count by country
        country_counts = Counter(result['country'] for result in results)
        
        print(f"\nCountry Distribution:")
        print("-" * 40)
        
        for country, count in country_counts.most_common():
            percentage = (count / total_subnets) * 100
            print(f"{country:<25} {count:>6} ({percentage:>5.1f}%)")
        
        # Additional statistics
        unique_countries = len([c for c in country_counts.keys() if c != 'Unknown'])
        unknown_count = country_counts.get('Unknown', 0)
        
        print(f"\nSummary Statistics:")
        print("-" * 40)
        print(f"Unique countries: {unique_countries}")
        print(f"Unknown locations: {unknown_count} ({unknown_count/total_subnets*100:.1f}%)")
        print(f"Successfully geolocated: {total_subnets - unknown_count} ({(total_subnets - unknown_count)/total_subnets*100:.1f}%)")
    
    def analyze_asn(self, asn: int, output_filename: Optional[str] = None) -> None:
        """
        Main method to analyze an ASN's geographical distribution.
        
        Args:
            asn: Autonomous System Number to analyze
            output_filename: Optional custom output filename
        """
        if output_filename is None:
            output_filename = f"asn_{asn}_geo_distribution.csv"
        
        try:
            # Step 1: Get prefixes from BGPView
            prefixes = self.get_asn_prefixes(asn)
            
            if not prefixes:
                print(f"No IPv4 prefixes found for ASN {asn}")
                return
            
            # Step 2: Break into /24 subnets
            subnets = self.break_into_c_class_subnets(prefixes)
            
            if not subnets:
                print("No valid subnets generated")
                return
            
            # Step 3: Geolocate subnets
            results = self.geolocate_subnets(subnets)
            
            # Step 4: Save to CSV
            self.save_to_csv(results, output_filename)
            
            # Step 5: Generate summary report
            self.generate_summary_report(results)
            
        except Exception as e:
            print(f"Error during analysis: {e}")
            sys.exit(1)


def main():
    """Main function to run the ASN geographical analyzer."""
    parser = argparse.ArgumentParser(
        description="Analyze geographical distribution of IP addresses for an Autonomous System"
    )
    parser.add_argument(
        'asn', 
        type=int, 
        help='Autonomous System Number to analyze (e.g., 138915)'
    )
    parser.add_argument(
        '-o', '--output',
        type=str,
        help='Output CSV filename (default: asn_{asn}_geo_distribution.csv)'
    )
    parser.add_argument(
        '-d', '--database',
        type=str,
        default='GeoLite2-City.mmdb',
        help='Path to MaxMind GeoLite2-City database file (default: GeoLite2-City.mmdb)'
    )
    
    args = parser.parse_args()
    
    # Validate ASN
    if args.asn <= 0 or args.asn > 4294967295:  # Max 32-bit ASN
        print("Error: ASN must be a positive integer between 1 and 4294967295")
        sys.exit(1)
    
    print(f"Starting geographical analysis for ASN {args.asn}")
    print(f"Using GeoIP database: {args.database}")
    
    analyzer = ASNGeoAnalyzer(args.database)
    analyzer.analyze_asn(args.asn, args.output)


if __name__ == "__main__":
    main()
